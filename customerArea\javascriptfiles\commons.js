var googleApiCaptcha = '6LcTjDUUAAAAADJeGeO5Ba_srEfHX3bkEKPVJ4i7';
var structureid = 0;
var deviseCode = "&euro;";
var tryingtodisconnect = false;

if (!window.console) {
    window.console = {
        log: function(str) {},
        warn: function(str) {},
        error: function(str) {},
        time: function(str) {},
        timeEnd: function(str) {}
        //alert(str);
    };
}

if (!('contains' in String.prototype)) {
    String.prototype.contains = function(str, startIndex) {
        "use strict";
        return -1 !== String.prototype.indexOf.call(this, str, startIndex);
    };
}

//permet de formater un integer sur autant de chiffres que l'on souhaite
//utiliser pour les structureid 0175
function numToNDigitStr(num, n) {
    if (num >= Math.pow(10, n - 1)) {
        return num;
    }
    return "0" + numToNDigitStr(num, n - 1);
}

$(document).ready(function() {

    //Affiche du compte client
    if(!settings.global.displayCustomerAccount){
        $('#myAccount, #infoCustomerBalance').hide();

    }


    //price format
    if ($('head').attr('devisecode') != undefined)
        deviseCode = $('head').attr('devisecode');
    AmountFormat()
    //date format
    $.each($("[data-datetoformat]"), function(i, k) {
        if($(k).attr("data-datelocale") != undefined && $(k).attr("data-datelocale") !="" ) {
            $(k).html(dateLocaleFormatter($(k).attr("data-datetoformat"), $(k).attr("data-datelocale")))
        }else {
            $(k).html(dateFormat($(k).attr("data-datetoformat")))
        }
    })
    //TRADS + inactivity
    structureid = numToNDigitStr($('#myhead').attr('structureid'), 4);

    $.each($('*[placeholder]'), function(indx, item) {
        $(item).attr('oldplaceholder', $(item).attr('placeholder'))
    });
    footerPlacement();
    LaunchTraduction();
    //
    $('#lnkdisconnect').on('click', function(e) {
        e.preventDefault();
        //modal permettant de savoir si on veut quitter 

        if (location.href.contains('attachments.aspx') && $('#previews .row.oneUpload').length > 0) {

            ShowGenericModal(ReadXmlTranslate('title_want_to_quit'), ReadXmlTranslate('msg_want_to_quit'), ReadXmlTranslate('btn_quit'), ReadXmlTranslate('btn_cancel'), 'btn-disconnect-confirm', '')

            $('#btn-disconnect-confirm').on('click', function(e) {
                e.preventDefault();

                tryingtodisconnect = true;
                deleteAllAttachments();

                Disconnect();
            });

        } else {

            Disconnect();
        }

    });

    /*
    // MASQUAGE DES PAGES 
    if (JSON.parse($('#settingsCustomer').text()).global != undefined && JSON.parse($('#settingsCustomer').text()).global.pageToHide != undefined) {
        // si l'appsettings de la structure contient pageToHide, on utilise les anciens setttings de gestion des pages
        console.log(settings.global.pageToHide)
        $.each(settings.global.pageToHide, function(indx, item) {
            $('#navbarNav li[data-pagename="' + item + '"]').addClass('d-none');
            $('#homeList li[data-pagename="' + item + '"]').addClass('d-none');
        })
    } else {
        //sinon -> nouveaux setttings de gestion des pages
        $.each(settings.global.pages, function(indx, item) {
            console.log(indx)
            if (item == false) {
                $('#navbarNav li[data-pagename="' + indx + '"]').addClass('d-none');
                $('#homeList li[data-pagename="' + indx + '"]').addClass('d-none');
            }
        })
    }
    */
    //remove hidden list from the DOM (to fix a css issue)
    $('#homeList li.homeItem.d-none').remove();


    // WHEN WIDGET ON AND ISWIDGET IS TRUE (BY DEFAULT IS FALSE)
    if (findGetParameter('iswidget') != 1 && !settings.global.isWidget) {
        $('.hideWhenWidget').removeClass('hideWhenWidget')
    } else {
        $('.showWhenWidget').removeClass('showWhenWidget')
    }

    if (settings.workspace.toUpperCase() != "PROD") {
        // ONLY FOR DEV
        $("body").append("<div id='overlay'>" + settings.workspace.toUpperCase() + "</div>");

        $("#overlay")
            .height('40px')
            .css({
                'opacity': 0.4,
                'color': '#FFF',
                'position': 'fixed',
                'top': 50,
                'right': -88,
                'padding': 10,
                'background-color': 'red',
                'width': '300px',
                'transform': 'rotate(55deg)',
                'transform-origin': 'center center',
                'z-index': 5000,
                'text-align': 'center'
            });
    }


    // construct the navbrand link
    $('.navbar-brand').attr('href', 'Home.aspx?idstructure=' + structureid+'&lang='+$('#receptVar').attr('data-lang'))
    // hide form-groups id it's set to FALSE in IHM
    $('[data-pageihm-visible="false"]').closest('.form-group').addClass('d-none');

    /** CHAMPS TEL INTERNATIONNAUX **/
    $('#tbTelephone, #attachInputPhonenumber').intlTelInput({
        nationalMode: true,
        placeholderNumberType: 'FIXED_LINE',
        autoPlaceholder: "polite",
        customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
            return ReadXmlTranslate('placeholder_phonenumber') + " ( " + selectedCountryPlaceholder + " )";
        },
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.13/js/utils.js" // just for formatting/placeholders etc
    });

    /** CHAMPS TEL INTERNATIONNAUX **/
    $('#tbFax, #attachInputFaxnumber').intlTelInput({
        nationalMode: true,
        placeholderNumberType: 'FIXED_LINE',
        autoPlaceholder: "polite",
        customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
            return ReadXmlTranslate('placeholder_faxnumber') + " ( " + selectedCountryPlaceholder + " )";
        },
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.13/js/utils.js" // just for formatting/placeholders etc
    });

    /** CHAMPS TEL INTERNATIONNAUX **/
    $('#tbPortable, #attachInputMobilenumber').intlTelInput({
        nationalMode: true,
        autoPlaceholder: "polite",
        customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
            return ReadXmlTranslate('placeholder_mobilenumber') + " ( " + selectedCountryPlaceholder + " )";
        },
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.13/js/utils.js" // just for formatting/placeholders etc
    });
    // add on every INPUTS "*" or "optionnal" when its required or not
    var itemRequired = '';
    if ($('[data-validator-type="datenaissance"]').attr('data-pageihm-mandatory') == 'false') {
        if (ReadXmlTranslate('lbl_optionnal') != "") {
            itemRequired = '(' + ReadXmlTranslate('lbl_optionnal') + ')';
        } else {
            itemRequired = '(Optionnal)';
        }
    } else {
        itemRequired = ""
    }

    var dobPlaceholder = ""
    dobPlaceholder += ReadXmlTranslate('placeholder_dob') != "" ? ReadXmlTranslate("placeholder_dob") : $('[data-validator-type="datenaissance"]').attr("oldplaceholder")
    dobPlaceholder += " : "
    dobPlaceholder += ReadXmlTranslate("pattern_dob_display") != "" ? ReadXmlTranslate("pattern_dob_display") : 'dd / MM / yyyy'
    dobPlaceholder += " "
    dobPlaceholder += itemRequired
    // initiate the DOB pattern + trads
    $('[data-validator-type="datenaissance"]').mask(ReadXmlTranslate('pattern_dob') != "" ? ReadXmlTranslate('pattern_dob') : 'dd / MM / yyyy', {
        'translation': {
            d: {
                pattern: /[0-9]/
            },
            M: {
                pattern: /[0-9]/
            },
            y: {
                pattern: /[0-9]/
            }
        },
        placeholder: dobPlaceholder

    });

    //par defaut on cache certains onglets (creation de compte...)
    var codesite = sessionStorage.getItem("codesite");

    if ((getUrlVars()["codesite"] != "" && getUrlVars()["codesite"] == "fans") || codesite != null) {
        $('#myTab li #aCreerCompte').addClass('hide');
        $('#menu li#liBankDetail').addClass('hide');
        $('#menu li#liTransactions').addClass('hide');
        $('#menu li#liFans a').removeClass('hide')
        $('li#liReaboFans').removeClass('hide');
        sessionStorage.setItem("codesite", "fans");
    } else {
        $('#myTab li #aCreerCompte').removeClass('hide');
        $('#menu li#liTransactions').removeClass('hide');
        $('#menu li#liBankDetail').removeClass('hide');
        $('#menu li#liFans').addClass('hide');
    }

    if (IsGestionnaireReabo()) {
        $('#menu li#liHistorique').addClass('hide');
        $('ul.myAccountList li a#lkHomeHisto').parents('li').addClass('hide');
    }

    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('#scroll').fadeIn();
        } else {
            $('#scroll').fadeOut();
        }
    });


    $('#scroll').click(function() {
        $("html, body").animate({
            scrollTop: 0
        }, 600);
        return false;
    });


    $('#scroll').attr('title', ReadXmlTranslate('scroll_to_top'));
    GetMenuLang();

    //ferme la modal
    $('#buttonCancel').on('click', function(e) {
        e.preventDefault();

        window.parent.postMessage("Closeme", "*");
    });


    loadGenericModal();


    $('#btn-show-password, #btn-show-password-create').off('click').on('click', function(e) {
        
        //etat du bouton
        var state = $(this).closest('.col-12').find('input').attr("type");

        //mot de passe en clair
        if(state == "text")
        {
            $(this).closest('.col-12').find('input[type="text"]').attr("type","password")
            $(this).find('svg').removeClass('fa-eye-slash').addClass('fa-eye');

        }else{
            $(this).closest('.col-12').find('input[type="password"]').attr("type","text")
            $(this).find('svg').removeClass('fa-eye').addClass('fa-eye-slash')
        }
       
    });





}); //fin ready


// function on RESIZE de la fenetre
$(window).resize(function(event) {
    footerPlacement()
});

// DETECT IF ISWIDGET
function iscustomerwidget() {
    if (findGetParameter('iswidget') != 1 && !settings.global.isWidget)
        return false;
    else
        return true;
}
// function pour placer le footer en bas d'�cran (seulement si le contenu est plus court que l'�cran)
function footerPlacement() {
    var footerHeight = $("#footer").outerHeight();
    $('#wrap').css('min-height', 'calc(100vh - ' + (footerHeight + 20) + 'px)')

}
// function de validation de date de naissance bas�e sur le pattern d�fini dans le TRANSLATE.[lang].xml
function validateDobWithPattern(date, isrequired) {

    if (isrequired == "false" && (date != "" && date != undefined) || isrequired == "true") {

        var wantedFormat = "DD/MM/YYYY"
        var actualPattern = ReadXmlTranslate('pattern_dob')
        var actualPatternSplit = actualPattern.split(/[^a-zA-Z]+/g);
        var actualDate = date
        var actualDateSplit = actualDate.split(/[^0-9]+/g);
        //console.log("actualDate : "+actualDate+"| actualPattern : "+actualPattern)
        //console.log("actualDate : "+actualDateSplit+"| actualPattern : "+actualPatternSplit)
        var doberror;
        var today = new Date();
        var day = '';
        var mounth = '';
        var year = '';
        $.each(actualPatternSplit, function(key, val) {
            if (val == 'dd') {
                if (actualDateSplit[key] > 0 && actualDateSplit[key] <= 31) {
                    day = actualDateSplit[key]
                    //console.log(day)
                } else {

                    doberror = true
                    //console.log('doberror day')
                }
            }


            if (val == "MM") {
                if (actualDateSplit[key] > 0 && actualDateSplit[key] <= 12) {
                    mounth = actualDateSplit[key]
                    //console.log(mounth)
                } else {

                    doberror = true
                    //console.log('doberror mounth')
                }
            }


            if (val == "yyyy") {

                //si le champ est vide dans Rodrigue alors on bypass la r�gle ==> 01/01/1900
                if (date.replace(/\s/g,'') == "01/01/1900"  || (actualDateSplit[key] > (today.getFullYear() - 120) && actualDateSplit[key] <= (today.getFullYear() - settings.global.minimalAgeToSignIn))) {
                    year = actualDateSplit[key]
                    //console.log(year) 
                } else {

                    doberror = true
                    //console.log('doberror year')
                }
            }
        })



        //console.log('doberror : '+doberror)
        if (!doberror) {
            return year + '-' + mounth + '-' + day;
        } else {
            return false;
        }
    }



}

function GetMenuLang() {
    var structureid = $('#myhead').attr('structureid');

    var languesList = getLanguesList();
    if (languesList.length > 1) {
        var htmlBtnLangues = '';

        $.each(languesList, function(indx, item) {
            htmlBtnLangues += '<li class="nav-item"><a href="" class="lang btn btn-sm btn-outline-secondary ml-1 mr-1" data-lang="' + item.langIso + '">' + item.langIso + '</a></li>';
        });

        $('#navbarRight').prepend(htmlBtnLangues);

        setLanguageClick();

    }

}

function setLanguageClick() {

    $('.lang').on('click', function(e) {
        e.preventDefault();
        var langselected = $(e.currentTarget).data('lang');

        if (getUrlVars()["lang"] == null) {
            if (document.location.href.contains('?')) {
                window.location.href = window.location.href + "&lang=" + langselected;
            } else {
                window.location.href = window.location.href + "?lang=" + langselected;
            }
        } else {
            window.location.href = window.location.origin + window.location.pathname + window.location.search.replace("lang=" + getUrlVars()["lang"], "lang=" + langselected);
        }
    });
}

function getLanguesList() {
    var languesList = [];
    var sData = JSON.stringify({
        idStructure: structureid
    });


    $.ajax({
        type: "POST",
        url: "commons.asmx/GetLanguesList",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        data: sData,
        async: false,
        success: function(data) {
            languesList = data.d;
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            //ShowModalError("modalMessage", "Erreur pendant le chargement des logos", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });

    return languesList;
}
//ADD console.log FOR INTERNET EXPLORER

if (!window.console) {
    var console = {
        log: function() {},
        warn: function() {},
        error: function() {},
        time: function() {},
        timeEnd: function() {}
    }
}

//LOADING FUNCTION
$(document).ajaxSend(function() {
    $('#loadingcircle').stop(true, true).removeClass('d-none').fadeTo(500, 1.0);
    $('#maincontent').stop(true, true).fadeTo(500, 0.5);
}).ajaxStop(function() {
    $('#loadingcircle').fadeTo(500, 0.0, function() {
        $(this).addClass('d-none');
    })
    $('#maincontent').fadeTo("slow", 1.0);
});

var config = {
    '.chosen-select': {
        width: "100%"
    }
}
for (var selector in config) {
    $(selector).chosen(config[selector]);
}



/*
if (getUrlVars()["opnpa"] == 1 && $("#linkconnectLoginPA_inMenu").length > 0) {    // auto open loginPA
    jQuery(document).ready(function () {
        $("#linkconnectLoginPA_inMenu").trigger('click');
    });
}


function divIdentifClose() {
    var txtIsIdentif = $get('ctl00_ContentPlaceHolder_txtIsIdentif');
    if (txtIsIdentif != null) {
        if (txtIsIdentif.value == "true") {
            $('#ctl00_ContentPlaceHolder_divIdentification').removeClass("invisible visible").addClass("invisible");
            var pgmeth = PageMethods.getPaimenturl(getPaimenturlSuccess, MethodeJS_Error);
        }
        else {
            var lkToPaie = $get('ctl00_ContentPlaceHolder_linkToPayement');
            if (lkToPaie != null)
                $('#ctl00_ContentPlaceHolder_linkToPayement').removeClass("invisible visible").addClass("invisible");
        }
    }
}
*/

// WHEN DISCONNECT OF CUSTOMER
function Disconnect() {
    // var pageMeth = PageMethods.DisconnectUser(DisconnectOk, DisconnectKo);

    $.ajax({
        type: "POST",
        url: "Commons.asmx/DisconnectUser",

        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function(msg) {
            // Do something interesting here.
            if (msg.d == "ok") {
                //envoie disconnect au parent
                window.parent.postMessage("Disconnect", "*");
                location.href = "login.aspx?idstructure=" + structureid;
                // window.location.reload();
            } else {
                window.location = $('#ctPlaceHold_hlHomePage').attr('href');
            }
        }
    });
}


function MethodeJS_Error(error, userContext, methodName) {
    //alert("!" + error.get_message());
    //alert('MethodeJS_Error');
    showAlertError('', error.get_message(), '')
}


function getUrlVars() {
    var vars = [],
        hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
    for (var i = 0; i < hashes.length; i++) {
        hash = hashes[i].split('=');
        vars.push(hash[0]);
        vars[hash[0]] = hash[1];
    }
    return vars;
}

function getLangForPhones() {

    var language = window.navigator.userLanguage || window.navigator.language;
    var _url = "";
    DEFAULT_VALUE = 'en';

    if (getUrlVars()["lang"] != undefined) {
        language = getUrlVars()["lang"]
    } else {
        PREFERRED_LANGUAGE = navigator.language || navigator.userLanguage || navigator.browserLanguage || navigator.systemLanguage || DEFAULT_VALUE;
        if (PREFERRED_LANGUAGE.contains('-'))
            language = PREFERRED_LANGUAGE.split('-')[1];
        else
            language = PREFERRED_LANGUAGE;
    }

    //si une langues est sp�cif�e dans les settings (permet de forcer pour des langues dans lesquels on ne peut pas r�cup�rer depuis le navigateur ex: suisse ==> CH)
    if (settings.global.langForPhones != "")
        return settings.global.langForPhones
    else
        return language;

    if (GetCountryDataIntlTelInput(language).length == 0 && language == "en")
        return "us";
}



var inactivityTime = function() {
    var t;
    window.onload = resetTimer;
    document.onmousemove = resetTimer;
    document.onkeypress = resetTimer;

    function logout() {

        ShowGenericModal(ReadXmlTranslate('title_inactivity'), ReadXmlTranslate('msg_inactivity'), ReadXmlTranslate('btn_got_it'), '', 'btn-inactivity', '')


        tryingtodisconnect = true;
        //deleteAllAttachments();
        Disconnect();
    }

    function resetTimer() {
        clearTimeout(t);
        t = setTimeout(logout, 600000); // 600000 = 10 minutes
        // 1000 milisec = 1 sec
    }
};



function GetUrlTemplate(pageName) {

    var language = window.navigator.userLanguage || window.navigator.language;
    var _url = "";

    var language = $('html').attr('lang');

    var eventid = "";
    if ($("head").attr('eventid') != undefined)
        eventid = $("head").attr('eventid');

    var paid = "";
    if ($("head").attr('paid') != undefined)
        paid = $("head").attr('paid');

    var structureid = $("head").attr('structureid');

    var str = "" + structureid
    var pad = "0000"
    var structureid = pad.substring(0, pad.length - str.length) + str

    var _url = "";


    var _urlEvtPaLang = "/files/" + structureid + "/" + customerDirectory + "/templates/" + pageName + "." + language + "." + eventid + "pa." + paid + ".htm";
    var _urlPaLang = "/files/" + structureid + "/" + customerDirectory + "/templates/" + pageName + "." + language + "pa." + paid + ".htm";
    var _urlEvtLang = "/files/" + structureid + "/" + customerDirectory + "/templates/" + pageName + "." + language + "." + eventid + ".htm";
    var _urlEvt = "/files/" + structureid + "/" + customerDirectory + "/templates/" + pageName + "." + eventid + ".htm";
    var _urlLang = "/files/" + structureid + "/" + customerDirectory + "/templates/" + pageName + "." + language + ".htm";
    var _urlDefaultWithStructure = "/files/" + structureid + "/" + customerDirectory + "/templates/" + pageName + ".htm";
    var _urlLangDefault = "/files/Default/" + customerDirectory + "/templates/" + pageName + "." + language + ".htm";
    var _urlDefaultWithLang = "/files/Default/" + customerDirectory + "/templates/" + pageName + ".fr.htm";
    var _urlDefault = "/files/Default/" + customerDirectory + "/templates/" + pageName + ".htm";


    if (fileExists(_urlEvtPaLang)) {
        _url = _urlEvtPaLang;
    } else if (fileExists(_urlPaLang)) {
        _url = _urlPaLang;
    } else if (fileExists(_urlEvtLang)) {
        _url = _urlEvtLang;
    } else if (fileExists(_urlEvt)) {
        _url = _urlEvt;
    } else if (fileExists(_urlLang)) {
        _url = _urlLang;
    } else if (fileExists(_urlDefaultWithStructure)) {
        _url = _urlDefaultWithStructure;
    } else if (fileExists(_urlLangDefault)) {
        _url = _urlLangDefault;
    } else if (fileExists(_urlPaLang)) {
        _url = _urlPaLang;
    } else if (fileExists(_urlDefaultWithLang)) {
        _url = _urlDefaultWithLang;
    } else if (fileExists(_urlDefault)) {
        _url = _urlDefault;
    } else {
        console.log("gros pb aucun fichier template");
    }


    console.log("default - url template : " + _url);
    return _url;

}



function GetCountryDataIntlTelInput(country) {

    var arrCountry = $.grep($.fn.intlTelInput.getCountryData(), function(indx, item) {
        return indx.iso2 == country;
    });

    return arrCountry;
}

// ADD EFFECT ON ROW AFTER UPDATE (utilis�e dans la page fans)

/*sfunction AddEffectToRowSelected(divID, objecttd) {
    //supprime le style sur la s�lection pr�c�dente
    $('#' + divID + ' tr td').removeAttr("style");
    //something you want delayed
    objecttd.contents('td').animate({ 'background-color': '#d9edf7!important' }, 2000, 'easeOutBounce');

}*/



// SYSTEME D'ALERT EN CAS DE SUCCESS
var myAlertSuccess;

function showAlertSuccess(title, message, timer) {
    title = title || '';
    message = message || '';
    timer = timer || 5000;
    clearTimeout(myAlertSuccess);
    $('#alertSuccess').addClass('show').css('display', 'block')
    $('#alertSuccess').find('h4').html(title);
    $('#alertSuccess').find('p').html(message);
    myAlertSuccess = setTimeout(function() {
        $('#alertSuccess').fadeOut(500).removeClass('show')
    }, timer);
}

// SYSTEME D'ALERT EN CAS D'ERREUR
var myAlertError;

function showAlertError(title, message, timer) {
    title = title || '';
    message = message || '';
    timer = timer || 5000;
    clearTimeout(myAlertError);
    $('#alertError').addClass('show').css('display', 'block')
    $('#alertError').find('h4').html(title);
    $('#alertError').find('p').html(message);
    myAlertError = setTimeout(function() {
        $('#alertError').fadeOut(500).removeClass('show')
    }, timer);
}

function ShowGenericModal(title, message, textbuttonaccept, textbuttonrefuse, idbuttonaccept, idbuttonrefuse, hasClose, hasKeyboard, hasBackdrop) {
    hasClose = hasClose || true;
    hasKeyboard = hasKeyboard || true;
    hasBackdrop = hasBackdrop || true;
    var modalContent = '<div class="modal-header">';

    if (title != '') {
        modalContent += '<h5 class="modal-title">' + title + '</h5>';
    }
    if (hasClose) {
        modalContent += '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>';
    }
    modalContent += '</div>';
    modalContent += '<div class="modal-body">' + message + '</div>';
    modalContent += '<div class="modal-footer"><div class="row"><div class="col-12">';

    if (textbuttonrefuse != "") {
        modalContent += ' <button type="button" class="col-12 col-md-auto btn btn-secondary" data-dismiss="modal" id="' + idbuttonrefuse + '">' + textbuttonrefuse + '</button>';
    }
    if (textbuttonaccept != "") {
        modalContent += ' <button type="button" class="col-12 col-md-auto btn btn-primary" id="' + idbuttonaccept + '" data-dismiss="modal">' + textbuttonaccept + '</button>';
    }
    modalContent += '</div></div></div>';

    //modalContent += '<div class="modal-footer"><div class="row"><div class="col-12"> <button type="button" class="col-12 col-md-auto btn btn-secondary" data-dismiss="modal" id="'+idbuttonrefuse+'">'+textbuttonrefuse+'</button>  <button type="button" class="col-12 col-md-auto btn btn-primary" id="'+idbuttonaccept+'" data-dismiss="modal">'+textbuttonaccept+'</button></div></div></div>';
    $('#modalGeneric').find('.modal-content').css('height', 'auto')
    $('#modalGeneric').find('.modal-content').html('')
    $('#modalGeneric').find('.modal-content').html(modalContent);

    $('#modalGeneric').modal({
        show: true,
        keyboard: hasKeyboard,
        backdrop: hasBackdrop
    });



}

/*
function ShowModalError(id, title, message, className, time) {

    var html = '<div class="' + className + '" id="alertDIV">' + message + '</div>';
    $("#" + id + " .modal-title").html(title);
    $("#" + id + " .modal-body").html(html);
    $("#" + id).modal('show');

    $("#" + id).modal('show').fadeIn(200);

    if (time != 0) {
        setTimeout(function () {
            $("#" + id).modal('hide')
        }, time);
    }
}

//utilis�e dans la page FANS 
function gestionMessageErreur(d) {

    var messagedisplay = ReadXmlTranslate("msg_error_add_relation");

    switch (d.code) {
        case 97:
        messagedisplay = ReadXmlTranslate("msg_error_add_relation_not_found");
        break;

        case 97:
        messagedisplay = ReadXmlTranslate("msg_error_add_relation_not_found");
        break;

        default: messagedisplay = d.message;
    }

   

    ShowModalError('modalMessage', ReadXmlTranslate("title_msg_error_add_relation"), messagedisplay, 'alert alert-warning', 5000);

    //    $('#modalMessage').modal('show');


}
*/


function GetUrlTemplateJsDataTable() {

    var language = window.navigator.userLanguage || window.navigator.language;
    var _url = "";
    DEFAULT_VALUE = 'en';

    if (getUrlVars()["lang"] != undefined) {
        language = getUrlVars()["lang"]
    } else {

        PREFERRED_LANGUAGE = navigator.language || navigator.userLanguage || navigator.browserLanguage || navigator.systemLanguage || DEFAULT_VALUE;
        language = PREFERRED_LANGUAGE.split('-')[0];
    }

    var _url = "";

    var _urlLangDefault = basUrl + "/javascriptfiles/datatableLanguages/" + language + ".js";


    if (fileExists(_urlLangDefault)) {
        _url = _urlLangDefault;
    } else {
        console.log("gros pb aucun fichier template");
    }


    console.log("default - url template : " + _url);
    return _url;

}


function GetLanguageDataTable() {
    //window.location.origin+ "/assets/js/pages/datatableLanguages/es.js"
    //http://cdn.datatables.net/plug-ins/725b2a2115b/i18n/ ==> pour avoir les langues des datatables 

    return $.ajax({
        url: GetUrlTemplateJsDataTable(),
        dataType: 'json',
        async: false
    }).responseJSON;


}

/*
//Fonction qui affiche le message d'erreur dans l'id defini en param�tre
//Le message se supprime au bout de 5 secondes
function ShowError(id, msg, className, _time) {
    var time = _time || 5000;
    //alert alert-danger alert-dismissable
    var errorDIV = ' <div class="' + className + '" id="alertDIV">  <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>  ' + msg + '</div>';
    $("#" + id).append(errorDIV).hide().show('slow');

    $("#datatableResult").hide();

    if (time > -1) {
        $("#alertDIV").fadeOut(time);
    }

}
*/



function GetDomainNameOfUrl(_url) {

    if (_url != undefined) {
        var url = _url;
        var url_parts = url.split('/');
        var domain_name_parts = url_parts[2].split(':');
        var domain_name = domain_name_parts[0];

        if (settings.workspace == "local") {
            return "http://" + url_parts[2];
        } else {
            return "https://" + domain_name;
        }
    }
}


function IsGestionnaireReabo() {
    // var pageMeth = PageMethods.DisconnectUser(DisconnectOk, DisconnectKo);
    var result = false;
    $.ajax({
        type: "POST",
        url: "Commons.asmx/IsGestionnaireReabo",
        contentType: "application/json; charset=utf-8",
        async: false,
        dataType: "json",
        success: function(msg) {
            // Do something interesting here.
            if (msg.d) {
                result = true;
            }

        }
    });

    return result;
}


//Efface les champs du formulaire passer en param�tre
function DeleteFormFields(idformulaire) {
    $(':input', '#' + idformulaire)
        .not(':button, :submit, :reset, :hidden')
        .val('')
        .removeAttr('checked')
        .removeAttr('selected');

    $('#' + idformulaire + ' select').val(0)

}
// FORM LOGIN CREATION VALIDATION
$(document).ready(function() {
    var navListItems = '#creationForm .nav .nav-item';
    var classSection = ".tab-pane";
    var allSection = $('#creationForm').find(classSection);
    var allNextBtn = $('#creationForm .next');
    var allPrevBtn = $('#creationForm .previous');

    var numTabs = $('#creationForm').find('.tab-pane').length;

    allNextBtn.each(function(index) {
        if (index === numTabs - 1) {
            //$( this ).find('.btn').html(ReadXmlTranslate('btn_creer_account'))
            $(this).addClass('finalstep');
        } else {
            //$( this ).find('.btn').html(ReadXmlTranslate('next'))
        }
    });

    $('#creationForm').find('.previous a').text(ReadXmlTranslate('previous'));

    $(navListItems).not(":eq(0)").addClass('disabled');
    $(navListItems).each(function(index) {
        //console.log(index) 
        $(this).find('.circle').css('z-index', (10 - index))
    })
    allSection.addClass('fade');



    $('[data-pageihm-visible="false"]').closest('.form-group').addClass('d-none');

    //allSection.tab('hide');
    $(navListItems).on('click', function(e) {
        e.preventDefault();
        var curStep = $(navListItems + '.active');
        var nextStep = $(this);
        var nextId = $(this).attr("href");
        var curId = curStep.attr('href');
        var curForm = $(classSection + '' + curId)
        var nextStepWizard = $(navListItems + '[href="' + nextId + '"]');
        //do validation
        if (validForm(curForm)) {
            curStep.removeClass('error')
            console.log('form valid')
            // if nextstep is AFTER the current step...
            if (nextStep.index() > curStep.index()) {
                // and the target clicked is not disabled...
                if (!$(this).hasClass('disabled')) {
                    nextStepWizard.removeClass('disabled').tab('show');
                }
            } else {
                $(this).tab('show') //or, show the target tab
            }
        } else {
            console.log('form NOT valid')
            if (nextStep.index() < curStep.index()) {
                $(this).tab('show')
            }
            curStep.addClass('error')

        }

    });


    allPrevBtn.click(function() {
        // console.log("prev")
        var curForm = $(this).closest(classSection);
        var curStepBtn = curForm.attr("id");
        var prevStepWizard = $(navListItems + '[href="#' + curStepBtn + '"]').prev();

        prevStepWizard.removeClass('disabled').trigger('click');
    });

    allNextBtn.click(function() {
        //console.log("next")
        var curStep = $(navListItems + '.active');
        var curForm = $(this).closest(classSection);
        var curStepBtn = curForm.attr("id");
        var nextStepWizard = $(navListItems + '[href="#' + curStepBtn + '"]').next();


        if (curStepBtn == "acount-tab") {
            if (!IsExistEmail()) {
                if (validForm(curForm)) {
                    if ($(this).hasClass('finalstep')) {
                        console.log('CreateCustomer')
                        CreateCustomer(false);
                    } else {
                        nextStepWizard.removeClass('disabled').tab('show');
                    }
                } //then do validation
            }
        } else {
            $.each($('input[required]:visible'), function(i, k) {
                var maRegex = /[a-zA-Z]+/;
                var verifie = maRegex.test($(k).html());
            })
            if (validForm(curForm)) {
                if ($(this).hasClass('finalstep')) {
                    console.log('CreateCustomer')
                    CreateCustomer(false);
                } else {
                    nextStepWizard.removeClass('disabled').tab('show');
                }
            } //then do validation
        }

        /*
           //do validation
           if (validForm(curForm)) {
               console.log('form valid')
               curStep.removeClass('error')
               if ($(this).hasClass('finalstep')) {
                   //console.log('CreateCustomer')
                   CreateCustomer();
               } else {
                   nextStepWizard.removeClass('disabled').tab('show');
               }
           } else {
               console.log('form NOT valid')
               curStep.addClass('error')
           }
           */

    });


});

// Fonction g�n�rique de validation de formulaire.
function validForm(thisform) {

    console.log('valid in progress : ' + thisform);

    var curInputs = thisform.find("input, select");
    var isValid = true;
    $(".form-group").removeClass("has-error");
    thisform.find('.card-header').removeClass('error');
    for (var i = 0; i < curInputs.length; i++) {
        //reset la validation du champs
        $(curInputs[i])[0].setCustomValidity('');
        // check password confirmation
        if ($(curInputs[i]).attr('data-validator-type') == 'confirmpassword' && !$(curInputs[i]).closest(".form-group").hasClass("d-none")) {
            if (!curInputs[i].validity.valid || $(curInputs[i]).val() != thisform.find('[data-validator-type="password"]').val()) {
                isValid = false;
                $(curInputs[i])[0].setCustomValidity('error');
                $(curInputs[i]).closest(".form-group").addClass("has-error");
                $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
            } else {
                $(curInputs[i])[0].setCustomValidity('');
            }
        }
        // check email confirmation
        else if ($(curInputs[i]).attr('data-validator-type') == 'confirmemail' && !$(curInputs[i]).closest(".form-group").hasClass("d-none")) {
            if (!curInputs[i].validity.valid || $(curInputs[i]).val() != thisform.find('[data-validator-type="email"]').val()) {
                isValid = false;
                $(curInputs[i])[0].setCustomValidity('error');
                $(curInputs[i]).closest(".form-group").addClass("has-error");
                $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
            } else {

                $(curInputs[i])[0].setCustomValidity('');

            }
        }
        // check TEL format
        else if (($(curInputs[i]).attr('type') == 'tel' || $(curInputs[i]).attr('data-validator-type') == 'phonenumber' || $(curInputs[i]).attr('data-validator-type') == 'faxnumber' || $(curInputs[i]).attr('data-validator-type') == 'portablenumber') && !$(curInputs[i]).closest(".form-group").hasClass("d-none")) {
            if ($(curInputs[i]).attr('data-pageihm-mandatory') == 'true' && !$(curInputs[i]).intlTelInput("isValidNumber")) {
                //si tel obligatoire et que le num�ro n'est pas valide
                isValid = false;
                $(curInputs[i])[0].setCustomValidity('error');
                $(curInputs[i]).closest('.intl-tel-input').addClass('error')
                $(curInputs[i]).closest(".form-group").addClass("has-error");
                $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
            } else if ($(curInputs[i]).attr('data-pageihm-mandatory') == 'false' && !$(curInputs[i]).intlTelInput("isValidNumber") && $(curInputs[i]).val() != '') {
                //si le tel est optionnel, rempli et pas valide
                isValid = false;
                $(curInputs[i])[0].setCustomValidity('error');
                $(curInputs[i]).closest('.intl-tel-input').addClass('error')
                $(curInputs[i]).closest(".form-group").addClass("has-error");
                $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
            } else {
                $(curInputs[i])[0].setCustomValidity('');
            }
        }
        // date of birth
        else if ($(curInputs[i]).attr('data-validator-type') == 'datenaissance' && !$(curInputs[i]).closest(".form-group").hasClass("d-none")) {
            if (!curInputs[i].validity.valid || validateDobWithPattern($(curInputs[i]).val(), $(curInputs[i]).attr('data-pageihm-mandatory')) == false) {
                isValid = false;
                $(curInputs[i])[0].setCustomValidity('error');
                $(curInputs[i]).closest(".form-group").addClass("has-error");
                $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
            } else {

                $(curInputs[i])[0].setCustomValidity('');

            }
        }
        // ddlnaming // ddlcountry // selectManifestations
        else if (($(curInputs[i]).attr('data-validator-type') == 'ddlnaming' || $(curInputs[i]).attr('data-validator-type') == 'ddlcountry' || $(curInputs[i]).attr('id') == 'waitingSelectEvents') && !$(curInputs[i]).closest(".form-group").hasClass("d-none")) {
            if (!curInputs[i].validity.valid || ($(curInputs[i]).prop('required') == true && $(curInputs[i]).val() == 0)) {
                isValid = false;
                $(curInputs[i])[0].setCustomValidity('error');
                $(curInputs[i]).closest(".form-group").addClass("has-error");
                $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
            } else {

                $(curInputs[i])[0].setCustomValidity('');

            }
        }
        // check postal code format
        else if ($(curInputs[i]).attr('data-validator-type') == 'zipcode' && !$(curInputs[i]).closest(".form-group").hasClass("d-none")) {
            var thisCpObj = []
            if (($('#ddlPays').val() != '0' || $('#attachSelectCountry').val() != '0') && ($('#ddlPays').val() != undefined || $('#attachSelectCountry').val() != undefined)) {
                // Vérifier que postalCodeData existe et a la propriété postalcode
                if (typeof postalCodeData !== 'undefined' && postalCodeData && postalCodeData.postalcode && Array.isArray(postalCodeData.postalcode)) {
                    try {
                        thisCpObj = $.grep(postalCodeData.postalcode, function(x) {
                            return x && x.ISO && x.ISO == ($('#ddlPays').val() || $('#attachSelectCountry').val())
                        })
                    } catch (e) {
                        console.warn('Erreur lors de la validation du code postal:', e);
                        thisCpObj = [];
                    }
                }
            }

            var thisCpRegex = new RegExp()
            if (thisCpObj != undefined && thisCpObj.length > 0) {
                thisCpRegex = new RegExp(thisCpObj[0].Regex);
            }

            if (!curInputs[i].validity.valid || (curInputs[i].validity.valid && !thisCpRegex.test($(curInputs[i]).val()) && $(curInputs[i]).val() != "")) {
                isValid = false;
                $(curInputs[i])[0].setCustomValidity('error');
                $(curInputs[i]).closest(".form-group").addClass("has-error");
                $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
            } else {
                $(curInputs[i])[0].setCustomValidity('');
            }
        }
        //check all input validity
        else if ((!curInputs[i].validity.valid || curInputs[i].validity.valid && !new RegExp(/[a-zA-Z0-9]+/g).test($(curInputs[i]).val()) && $(curInputs[i]).val() != "") && !$(curInputs[i]).closest(".form-group").hasClass("d-none")) {
            isValid = false;
            $(curInputs[i])[0].setCustomValidity('error');
            $(curInputs[i]).closest(".form-group").addClass("has-error");
            $(curInputs[i]).closest('.card').find('.card-header').addClass('error');
        }
    }

    thisform.addClass('was-validated');
    if (isValid) {
        thisform.removeClass('was-validated');
        return true;
    } else {
        thisform.addClass('error');
        return false;
    }
}
//resize l'iframe parent.
function resizeParentIframe() {
    var heightpage = $(document).height();
    var objmessage = {
        heightpage: heightpage,
        id: "iframecustomer"
    };
    window.parent.postMessage(objmessage, "*");
    //window.parent.postMessage("height:" + heightpage , "*");

    //changement de tabs dans login
    $('a[data-toggle="tab"], .nav-pills a').on('shown.bs.tab', function(e) {
        e.target // newly activated tab
        e.relatedTarget // previous active tab
        var heightpage = e.currentTarget.closest('body').clientHeight;
        var objmessage = {
            heightpage: heightpage,
            id: "iframecustomer"
        };
        window.parent.postMessage(objmessage, "*");
        //window.parent.postMessage("height:" + heightpage , "*");
    })


    $(window).resize(function(event) {
        var heightpage = $('body').height();
        var objmessage = {
            heightpage: heightpage,
            id: "iframecustomer"
        };
        window.parent.postMessage(objmessage, "*");
        // window.parent.postMessage("height:" + heightpage , "*");
    });
}

// VERIF EXIST EMAIL WHEN CREATE NEW PROFIL
function IsExistEmail() {

    var result = false;
    var structureid = $('#myhead').attr('structureid');
    var email = $('#tbEmail').val();
    var sData = JSON.stringify({
        idStructure: structureid,
        email: email
    });

    if (email != "") {
        $.ajax({
            type: "POST",
            url: 'login.aspx/IsExistEmail',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            async: false,
            success: function(data) {
                if (data.d.split(':')[0] == "true") {
                    showAlertError('', ReadXmlTranslate("msg_error_existing_email_already"), 5000);
                    // alert(ReadXmlTranslate("msg_error_existing_email_already"))
                    result = true;

                }


            },
            error: function(XMLHttpRequest, textStatus, errorThrown) {
                // ShowError("msgNotificationAlert", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace, "alert alert-danger alert-dismissable", -1);
                console.log(XMLHttpRequest.responseJSON + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);

            }
        });

        return result;
    }
    return false;
}

// VERIF IF FILE EXIST
function fileExists(testUrl) {
    var http = jQuery.ajax({
        type: "HEAD", //Not get
        url: testUrl,
        async: false
    })
    return http.status != 404;
}


function findGetParameter(parameterName) {
    var result = null,
        tmp = [];
    var items = location.search.substr(1).split("&");
    for (var index = 0; index < items.length; index++) {
        tmp = items[index].split("=");
        if (tmp[0] === parameterName) result = decodeURIComponent(tmp[1]);
    }
    return result;
}

function IsIE(userAgent) {
    userAgent = userAgent || navigator.userAgent;
    return userAgent.indexOf("MSIE ") > -1 || userAgent.indexOf("Trident/") > -1;
}

function IsSafari() {

    var is_safari = navigator.userAgent.toLowerCase().indexOf('safari/') > -1;
    return is_safari;

}


function loadGenericModal() {

    $('#modalGeneric').on('show.bs.modal', function(event) {
        var button = $(event.relatedTarget) // Button that triggered the modal
        var modalType = button.data('type') // Extract info from data-type attribute 
        var modalTitle = (button.data('title') != "" && button.data('title') != undefined) ? button.data('title') : "" // Extract info from data-title attribute
        var modalHref = (button.attr('href') != "") ? button.attr('href') : button.data('href') // Extract info from data-href attribute
        if (button.data('filename') != undefined)
            button.data('filename', button.data('filename').replace('[lang]', $('html').attr('lang').toLowerCase()))

        switch (modalType) {

            case "iframe":
                var $modal = $(this)

                var modal = '<div class="modal-header">';
                modal += '<h5 class="modal-title">' + modalTitle + '</h5>';
                modal += '<button type="button" class="close" data-dismiss="modal" aria-label="Close">';
                modal += '<span aria-hidden="true">&times;</span>';
                modal += '</button>';
                modal += '</div>';
                var modalContent = '<div class="modal-body"><iframe width="100%" style="position: absolute;   top: 0;   left: 0;     height: 100%;"frameborder="0" scrolling="yes" id="iframecustomer"  allowtransparency="true"></iframe></div>';

                $modal.find('.modal-content').html(modal + '' + modalContent);
                $modal.find('.modal-content').css('height', '80vh')
                $modal.find('iframe').attr('src', modalHref);

                break;

        }

    })

}

//sort by
/* doc : http://www.vnykmshr.com/sort-array-of-objects-by-multiple-fields */
function predicate() {
    var fields = [],
        n_fields = arguments.length,
        field, name, reverse, cmp;

    var default_cmp = function(a, b) {
            if (a === b) return 0;
            return a < b ? -1 : 1;
        },
        getCmpFunc = function(primer, reverse) {
            var dfc = default_cmp,
                // closer in scope
                cmp = default_cmp;
            if (primer) {
                cmp = function(a, b) {
                    return dfc(primer(a), primer(b));
                };
            }
            if (reverse) {
                return function(a, b) {
                    return -1 * cmp(a, b);
                };
            }
            return cmp;
        };

    // preprocess sorting options
    for (var i = 0; i < n_fields; i++) {
        field = arguments[i];
        if (typeof field === 'string') {
            name = field;
            cmp = default_cmp;
        } else {
            name = field.name;
            cmp = getCmpFunc(field.primer, field.reverse);
        }
        fields.push({
            name: name,
            cmp: cmp
        });
    }

    // final comparison function
    return function(A, B) {
        var a, b, name, result;
        for (var i = 0; i < n_fields; i++) {
            result = 0;
            field = fields[i];
            name = field.name;

            result = field.cmp(A[name], B[name]);
            if (result !== 0) break;
        }
        return result;
    };
}

function AmountFormat() {
    if( $('[data-priceformat]').length > 0)
    {

        $('[data-priceformat]').priceFormat({
            prefix: '',
            suffix: ' ' + deviseCode,
            thousandsSeparator: ' '
        })

    }
    
}

function dateFormat(date) {
    var datetmp = (date).split(' ')[0].split('/')
    var day = datetmp[0]
    var month = datetmp[1]
    var year = datetmp[2]
    var dateformated = new Date(year + "-" + month + "-" + day).toLocaleDateString(getLang())
    return dateformated
}


function dateLocaleFormatter(date, format) {
    var datetmp = (date).split(' ')[0].split('/')
    var timetmp = (date).split(' ')[1].split(':')

    var day = datetmp[0]
    var month = (datetmp[1]-1)
    var year = datetmp[2]

    var hour = timetmp[0]
    var minutes = timetmp[1]
    var seconds = timetmp[2]

    formatArr = format.split(",")
    var options = { year: 'numeric', day: 'numeric'};
    $.each(formatArr, function(i,k) {
        if(k == "day2")
                options["day"] = '2-digit'
        if(k == "day")
                options["day"] = 'numeric'
        if(k == "monthlong")
                options["month"] = 'long'
        if(k == "month")
                options["month"] = '2-digit'
        if(k == "weekday")
                options["weekday"] = 'long'
        if(k == "hour")
                options["hour"] = '2-digit'
        if(k == "minutes")
                options["minute"] = '2-digit'
        if(k=="secondes")
            options["second"] = '2-digit'
    })

    var dateformated = new Date(year, month, day, hour, minutes, seconds).toLocaleString(getLang(), options)
    return dateformated
}
