<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test PayPal - Bouton de connexion</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="https://www.paypalobjects.com/js/external/api.js"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container mt-5">
            <div class="row">
                <div class="col-md-6 offset-md-3">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h3><i class="fab fa-paypal"></i> Test PayPal Login</h3>
                        </div>
                        <div class="card-body text-center">

                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Configuration</h5>
                                <p><strong>Structure :</strong> 0426</p>
                                <p><strong>SDK :</strong> Sandbox</p>
                                <p><strong>URL de redirection :</strong><br>
                                <small>https://dev.themisweb.fr/customer/_loginPayPal.aspx?idstructure=0426</small></p>
                            </div>

                            <h4>Connexion avec PayPal</h4>
                            <div id="paypal-login-button" class="mt-3 mb-3"></div>

                            <div class="mt-3">
                                <h5>Ou utilisez ce bouton de secours :</h5>
                                <button type="button" class="btn btn-primary btn-lg" onclick="loginWithPayPalSandbox()">
                                    <i class="fab fa-paypal"></i> Se connecter avec PayPal (Sandbox)
                                </button>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> Ce bouton utilise :</h6>
                                <ul class="text-left mb-0">
                                    <li>✅ SDK Sandbox (pas production)</li>
                                    <li>✅ Vos nouvelles clés PayPal</li>
                                    <li>✅ URL de redirection correcte</li>
                                    <li>✅ Structure ID correcte (0426)</li>
                                </ul>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <script>
        var clientId = "AZKOsIycBytFxeUfRdiRDtI6rmqT8m96xaw4KBymF9hk44HjSw_ubrQwdI1QZz_-RijZvaIQKLAq93Jw";
        var returnUrl = "https://dev.themisweb.fr/customer/Login.aspx?idstructure=426";

        $(document).ready(function() {
            console.log('Initialisation du bouton PayPal...');
            console.log('Client ID:', clientId.substring(0, 15) + '...');
            console.log('Return URL:', returnUrl);

            // Créer le bouton PayPal unique avec configuration sandbox
            paypal.use(['login'], function (login) {
                login.render({
                    "appid": clientId,
                    "scopes": "openid email profile",
                    "containerid": "paypal-login-button",
                    "responseType": "code",
                    "locale": "fr-fr",
                    "buttonType": "LWP",
                    "buttonShape": "rectangle",
                    "buttonSize": "lg",
                    "fullPage": "true",
                    "returnurl": returnUrl,
                    "state": "idstructure=0426",
                    "sandbox": true  // ✅ Force l'utilisation du sandbox
                });
            });

            console.log('Bouton PayPal créé avec succès !');
        });

        // Fonction de secours pour redirection manuelle vers sandbox
        function loginWithPayPalSandbox() {
            var paypalSandboxUrl = "https://www.sandbox.paypal.com/signin/authorize?" +
                "flowEntry=static&" +
                "client_id=" + clientId + "&" +
                "response_type=code&" +
                "scope=openid%20email%20profile&" +
                "redirect_uri=" + encodeURIComponent(returnUrl) + "&" +
                "state=idstructure=0426";

            console.log('Redirection vers sandbox:', paypalSandboxUrl);
            window.location.href = paypalSandboxUrl;
        }
    </script>
</body>
</html>
