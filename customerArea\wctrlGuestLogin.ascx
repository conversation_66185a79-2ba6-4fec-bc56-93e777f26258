﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="wctrlGuestLogin.ascx.cs" Inherits="customerArea.wctrlGuestLogin" %>
<!-- tabindex : 3 -->
<!-- msg entete -->
<div id="diventeteguestlogin" runat="server">
    <p class="text-center" data-trad="msg_entente_guest_login"></p>
</div>
<div id="guestForm" class="form-horizontal" role="form">
    <!-- prenom -->
    <div class="form-group">
        <div class="row">
            <label class="col-12 control-label sr-only">
                 <asp:Label ID="lbPrenomGuest" runat="server" for="tbPrenomGuest" data-trad="lbl_firstname"></asp:Label>
            </label>
        </div>
        <div class="row">
            <div class="col-12">
                <asp:TextBox ID="tbPrenomGuest" runat="server" MaxLength="50"  autocomplete="off" type="text" AutoCompleteType="None"  data-validator-type="firstname" class="form-control input-sm" ClientIDMode="Static" placeholder="firstname" data-tradplaceholder="placeholder_firstname" tabindex=3></asp:TextBox>
                <div class="invalid-feedback" data-trad="msg_error_firstname">Your email is not identical</div>
            </div>
            <!--input type="text" class="form-control" name="email" /-->
        </div>
    </div>
    <!-- nom -->
    <div class="form-group">
        <div class="row">
            <label class="col-12 control-label sr-only">
                <asp:Label ID="lbNomGuest" runat="server" for="tbNomGuest" data-trad="lbl_lastname"></asp:Label>
            </label>
        </div>
        <div class="row">
            <div class="col-12">
                <asp:TextBox ID="tbNomGuest" runat="server" name="lastname" MaxLength="50" autocomplete="off" AutoCompleteType="None"  type="text" class="form-control input-sm" ClientIDMode="Static" data-validator-type="name" placeholder="Lastname" data-tradplaceholder="placeholder_lastname" tabindex=3></asp:TextBox>
                <div class="invalid-feedback" data-trad="msg_error_lastname">Please enter an valid email</div>
            </div>
            <!--input type="text" class="form-control" name="email" /-->
        </div>
    </div>
    
    <!-- email -->
    <div class="form-group">
        <div class="row">
            <label class="col-12 control-label sr-only">
                <asp:Label ID="lbEmailGuest" runat="server" for="tbEmailGuest" data-trad="lbl_email"></asp:Label>
            </label>
        </div>
        <div class="row">
            <div class="col-12">
                <asp:TextBox ID="tbEmailGuest" runat="server" MaxLength="50" autocomplete="off" data-validator-type="email" AutoCompleteType="None" type="email"  class="form-control input-sm" ClientIDMode="Static" placeholder="email" data-tradplaceholder="placeholder_guest_email" tabindex=3></asp:TextBox>
                <div class="invalid-feedback" data-trad="msg_error_email">Please enter an valid email</div>
            </div>
            <!--input type="text" class="form-control" name="email" /-->
        </div>
    </div>

    <!-- code postal -->
    <div class="form-group">
        <div class="row">
            <label class="col-12 control-label sr-only">
                <asp:Label ID="lbPostalCodeGuest" runat="server" for="tbPostalCodeGuest" data-trad="lbl_cp"></asp:Label>
            </label>
        </div>
        <div class="row">
            <div class="col-12">
                <asp:TextBox ID="tbPostalCodeGuest" runat="server" MaxLength="10" autocomplete="off" AutoCompleteType="HomeZipCode" type="text" class="form-control input-sm" ClientIDMode="Static" data-validator-type="zipcode" placeholder="Code postal" data-tradplaceholder="placeholder_cp" tabindex=3></asp:TextBox>
                <div class="invalid-feedback" data-trad="msg_error_cp">Please enter a valid postal code</div>
            </div>
        </div>
    </div>
   
    
    <button id="btnCreateGuestAccount" class="btn btn-primary btn-block" tabindex=3><span data-trad="btn_continue_without_account" data-title="btn_continue_without_account"></span></button>
</div>
<div id="divexplainguestlogin" class="mt-3" runat="server">
    <p class="text-center" data-trad="msg_explain_guest_login"></p>
</div>

<script type="text/javascript">
    // Initialiser postalCodeData pour éviter les erreurs JavaScript
    var postalCodeData = postalCodeData || { postalcode: [] };
</script>